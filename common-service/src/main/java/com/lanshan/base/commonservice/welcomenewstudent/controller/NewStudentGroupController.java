package com.lanshan.base.commonservice.welcomenewstudent.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.group.qo.SendGroupChatMessageQo;
import com.lanshan.base.commonservice.group.qo.UpdateGroupChatQo;
import com.lanshan.base.commonservice.group.vo.GroupChatVO;
import com.lanshan.base.commonservice.welcomenewstudent.qo.NewStudentGroupQO;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentGroupService;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentGroupVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * 新生群表(NewStudentGroup)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("newStudentGroup")
@Api(tags = "新生群表(NewStudentGroup)控制层", hidden = true)
public class NewStudentGroupController {
    /**
     * 服务对象
     */
    @Resource
    private NewStudentGroupService newStudentGroupService;

    /**
     * 分页查询所有数据
     *
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping("/page")
    public Result<IPage<NewStudentGroupVO>> page(NewStudentGroupQO qo) {
        return Result.build(newStudentGroupService.pageByParam(qo));
    }

    //public Result

    @ApiOperation("导出新生群")
    @GetMapping("/export")
    public void exportGroup(HttpServletResponse response) {
        newStudentGroupService.exportGroup(response);
    }

    @ApiOperation("导入新生群")
    @PostMapping("/import")
    public Result<Boolean> importGroup(MultipartFile file) {
        return Result.build(newStudentGroupService.importGroup(file));
    }

    @ApiOperation("导出新生群错误数据")
    @GetMapping("/exportError")
    public void exportError(HttpServletResponse response) {
        newStudentGroupService.exportError(response);
    }

    @ApiOperation("修改群主")
    @PostMapping("/changeGroupOwner")
    public Result<Boolean> changeGroupOwner(@RequestBody UpdateGroupChatQo vo) {
        return Result.build(newStudentGroupService.changeGroupOwner(vo));
    }

    @ApiOperation("发送群消息")
    @PostMapping("/sendGroupMsg")
    public Result<Boolean> sendGroupMsg(SendGroupChatMessageQo qo) {
        return Result.build(newStudentGroupService.sendGroupMsg(qo));
    }

    @ApiOperation("根据群聊 ID 获取群信息")
    @GetMapping("/getGroupChat")
    public Result<GroupChatVO> getGroupChat(String chatId) {
        return Result.build(newStudentGroupService.getGroupChat(chatId));
    }

    @ApiOperation("测试 joinInUser 字段查询")
    @GetMapping("/testJoinInUser")
    public Result<String> testJoinInUser() {
        try {
            newStudentGroupService.testJoinInUserQuery();
            return Result.build("测试完成，请查看日志");
        } catch (Exception e) {
            return Result.build().error("测试失败: " + e.getMessage());
        }
    }
}

