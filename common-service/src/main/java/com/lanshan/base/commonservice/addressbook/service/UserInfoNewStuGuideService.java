package com.lanshan.base.commonservice.addressbook.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.addressbook.entity.UserInfoNewStuGuide;
import com.lanshan.base.commonservice.addressbook.qo.UserInfoNewStuGuideQO;
import com.lanshan.base.commonservice.addressbook.vo.UserInfoNewStuGuideVO;

import java.util.List;

/**
 * 新生指南表(UserInfoNewStuGuide)表服务接口
 *
 * <AUTHOR>
 */
public interface UserInfoNewStuGuideService extends IService<UserInfoNewStuGuide> {

    /**
     * 分页查询所有数据
     *
     * @param qo 查询条件
     * @return 对象列表
     */
    IPage<UserInfoNewStuGuideVO> pageByParam(UserInfoNewStuGuideQO qo);

    /**
     * 查询所有数据
     *
     * @return 列表
     */
    List<UserInfoNewStuGuideVO> listAll();

    /**
     * 获取最大排序
     *
     * @return 最大排序
     */
    Integer getMaxSort();
}

