package com.lanshan.base.commonservice.typehandler;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * PostgreSQL jsonb 类型的 List<String> 处理器
 * 专门用于处理数据库中存储为 jsonb 格式的字符串列表
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@MappedJdbcTypes({JdbcType.OTHER})
@MappedTypes(List.class)
public class JsonbListTypeHandler extends BaseTypeHandler<List<String>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType) throws SQLException {
        try {
            // 创建 PostgreSQL jsonb 对象
            PGobject jsonbObject = new PGobject();
            jsonbObject.setType("jsonb");
            jsonbObject.setValue(JSON.toJSONString(parameter));
            ps.setObject(i, jsonbObject);
            
            log.debug("设置 jsonb 参数: {}", JSON.toJSONString(parameter));
        } catch (Exception e) {
            log.error("设置 jsonb 参数失败", e);
            throw new SQLException("Failed to set jsonb parameter", e);
        }
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseJsonbToList(rs.getObject(columnName), columnName);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseJsonbToList(rs.getObject(columnIndex), "column_" + columnIndex);
    }

    @Override
    public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseJsonbToList(cs.getObject(columnIndex), "column_" + columnIndex);
    }

    /**
     * 解析 jsonb 对象为 List<String>
     * 
     * @param value 从数据库获取的值
     * @param identifier 字段标识符，用于日志
     * @return 解析后的字符串列表
     */
    private List<String> parseJsonbToList(Object value, String identifier) {
        if (value == null) {
            log.debug("字段 {} 的值为 null", identifier);
            return Collections.emptyList();
        }

        try {
            String jsonString = null;
            
            // 处理 PostgreSQL PGobject
            if (value instanceof PGobject) {
                PGobject pgObject = (PGobject) value;
                String type = pgObject.getType();
                String pgValue = pgObject.getValue();
                
                log.debug("字段 {} PGobject 类型: {}, 值: {}", identifier, type, pgValue);
                
                if (Objects.nonNull(pgValue) && ("jsonb".equals(type) || "json".equals(type))) {
                    jsonString = pgValue;
                } else {
                    log.warn("字段 {} PGobject 类型不匹配或值为空，类型: {}, 值: {}", identifier, type, pgValue);
                    return Collections.emptyList();
                }
            } 
            // 处理普通字符串
            else if (value instanceof String) {
                jsonString = (String) value;
                log.debug("字段 {} 字符串值: {}", identifier, jsonString);
            }
            // 处理其他类型
            else {
                log.warn("字段 {} 未知类型: {}, 值: {}", identifier, value.getClass().getName(), value);
                return Collections.emptyList();
            }

            // 解析 JSON 字符串
            if (jsonString != null && !jsonString.trim().isEmpty()) {
                // 处理可能的转义字符
                jsonString = jsonString.trim();
                
                // 如果是简单的数组格式，直接解析
                if (jsonString.startsWith("[") && jsonString.endsWith("]")) {
                    List<String> result = JSON.parseObject(jsonString, new TypeReference<List<String>>() {});
                    log.debug("字段 {} 解析成功，结果: {}", identifier, result);
                    return result != null ? result : Collections.emptyList();
                } else {
                    log.warn("字段 {} JSON 格式不正确: {}", identifier, jsonString);
                    return Collections.emptyList();
                }
            } else {
                log.debug("字段 {} JSON 字符串为空", identifier);
                return Collections.emptyList();
            }
            
        } catch (Exception e) {
            log.error("解析字段 {} 的 jsonb 值失败，原始值: {}, 类型: {}", 
                    identifier, value, value != null ? value.getClass().getName() : "null", e);
            return Collections.emptyList();
        }
    }
}
