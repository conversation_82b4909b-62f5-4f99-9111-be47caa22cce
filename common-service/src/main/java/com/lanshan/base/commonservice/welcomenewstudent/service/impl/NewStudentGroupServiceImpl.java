package com.lanshan.base.commonservice.welcomenewstudent.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import com.lanshan.base.commonservice.group.qo.GetGroupChatQo;
import com.lanshan.base.commonservice.group.qo.SendGroupChatMessageQo;
import com.lanshan.base.commonservice.group.qo.UpdateGroupChatQo;
import com.lanshan.base.commonservice.group.service.GroupChatService;
import com.lanshan.base.commonservice.group.vo.GroupChatVO;
import com.lanshan.base.commonservice.system.service.ISysConfigService;
import com.lanshan.base.commonservice.welcomenewstudent.converter.NewStudentGroupConverter;
import com.lanshan.base.commonservice.welcomenewstudent.dao.NewStudentGroupDao;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentGroup;
import com.lanshan.base.commonservice.welcomenewstudent.excel.NewStudentGroupExportDTO;
import com.lanshan.base.commonservice.welcomenewstudent.excel.NewStudentGroupImportDTO;
import com.lanshan.base.commonservice.welcomenewstudent.excel.listener.NewStuGroupImportListener;
import com.lanshan.base.commonservice.welcomenewstudent.qo.NewStudentGroupQO;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentGroupService;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentGroupVO;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;

/**
 * 新生群表(NewStudentGroup)表服务实现类
 *
 * <AUTHOR>
 */
@Service("newStudentGroupService")
public class NewStudentGroupServiceImpl extends ServiceImpl<NewStudentGroupDao, NewStudentGroup> implements NewStudentGroupService {

    @Resource
    private RedisService redisService;

    @Resource
    private GroupChatService groupChatService;

    @Resource
    private ISysConfigService sysConfigService;

    @Override
    public IPage<NewStudentGroupVO> pageByParam(NewStudentGroupQO qo) {
        return this.page(Page.<NewStudentGroup>of(qo.getPage(), qo.getSize()), Wrappers.lambdaQuery(NewStudentGroup.class)
                .like(StringUtils.isNotEmpty(qo.getGroupName()), NewStudentGroup::getGroupName, qo.getGroupName())
                .like(StringUtils.isNotEmpty(qo.getOwnerName()), NewStudentGroup::getOwnerName, qo.getOwnerName())
                .like(StringUtils.isNotEmpty(qo.getOwnerUserid()), NewStudentGroup::getOwnerUserid, qo.getOwnerUserid())
                .like(StringUtils.isNotEmpty(qo.getDeptName()), NewStudentGroup::getGroupName, qo.getDeptName())
                .orderByDesc(NewStudentGroup::getCreateDate)
        ).convert(NewStudentGroupConverter.INSTANCE::toVO);
    }

    @Override
    public Boolean importGroup(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("请选择要上传的Excel文件");
        }
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !(originalFilename.endsWith(".xls") || originalFilename.endsWith(".xlsx"))) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("请上传Excel格式的文件(.xls或.xlsx)");
        }
        if (file.getSize() > 100 * 1024 * 1024) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("导入文件不能大于100M");
        }
        String agentId = sysConfigService.selectConfigByKey("welcome.new.stu.agentId");
        try (InputStream is = file.getInputStream()) {
            EasyExcel.read(is)
                    .head(NewStudentGroupImportDTO.class)
                    .sheet()
                    .headRowNumber(1)
                    .registerReadListener(new NewStuGroupImportListener(agentId, groupChatService, redisService, this))
                    .doRead();
            return !redisService.hasKey(CommonServiceRedisKeys.NEW_STUDENT_GROUP_IMPORT_ERROR_LIST);
        } catch (IOException e) {
            log.error("导入新生数据异常", e);
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("导入新生数据异常");
        }
    }

    @Override
    public void exportGroup(HttpServletResponse response) {
        List<NewStudentGroup> list = this.list(Wrappers.lambdaQuery(NewStudentGroup.class).isNull(NewStudentGroup::getChatId));
        List<NewStudentGroupImportDTO> importDTO = NewStudentGroupConverter.INSTANCE.toImportDTO(list);
        try (ServletOutputStream os = response.getOutputStream()) {
            EasyExcel.write(os)
                    .head(NewStudentGroupImportDTO.class)
                    .sheet()
                    .doWrite(importDTO);
        } catch (IOException e) {
            log.error("导出未建群数据异常", e);
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("导出未建群数据异常");
        }
    }

    @Override
    public void exportError(HttpServletResponse response) {
        if (!redisService.hasKey(CommonServiceRedisKeys.NEW_STUDENT_GROUP_IMPORT_ERROR_LIST)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("未找到错误数据");
        }
        try {
            response.setContentType("application/octet-stream;charset=UTF-8");
            List<NewStudentGroupExportDTO> errorList = redisService.getCacheList(CommonServiceRedisKeys.NEW_STUDENT_GROUP_IMPORT_ERROR_LIST);
            EasyExcel.write(response.getOutputStream())
                    .head(NewStudentGroupExportDTO.class)
                    .sheet()
                    .doWrite(errorList);
        } catch (IOException e) {
            log.error("导出群聊错误数据异常", e);
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("导出群聊错误数据异常");
        }
    }

    @Override
    public Boolean changeGroupOwner(UpdateGroupChatQo qo) {
        try {
            String agentId = sysConfigService.selectConfigByKey("welcome.new.stu.agentId");
            qo.setAgentId(agentId);
            groupChatService.updateGroup(qo);
            //更新新生群聊表
            this.update(Wrappers.lambdaUpdate(NewStudentGroup.class)
                    .set(NewStudentGroup::getOwnerName, qo.getOwnerName())
                    .set(NewStudentGroup::getOwnerUserid, qo.getOwner())
                    .eq(NewStudentGroup::getChatId, qo.getChatId()));
            return true;
        } catch (WxErrorException e) {
            log.error("修改群聊群主异常", e);
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("修改群聊群主异常，" + e.getMessage());
        }
    }

    @Override
    public Boolean sendGroupMsg(SendGroupChatMessageQo qo) {
        try {
            String agentId = sysConfigService.selectConfigByKey("welcome.new.stu.agentId");
            qo.setAgentId(agentId);
            groupChatService.sendGroupChatMessage(qo);
            return true;
        } catch (Exception e) {
            log.error("发送群消息异常", e);
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("发送群消息异常，" + e.getMessage());
        }
    }

    @Override
    public GroupChatVO getGroupChat(String chatId) {
        Long groupChatId = groupChatService.getGroupIdByChatId(chatId);
        if (Objects.isNull(groupChatId)) {
            return null;
        }
        GetGroupChatQo qo = new GetGroupChatQo();
        qo.setId(groupChatId);
        return groupChatService.getGroupChatById(qo);
    }

    /**
     * 测试方法：验证 joinInUser 字段查询
     * 用于调试 jsonb 字段查询问题
     */
    public void testJoinInUserQuery() {
        try {
            log.info("开始测试 joinInUser 字段查询...");

            // 查询一条记录进行测试
            NewStudentGroup testGroup = this.getOne(Wrappers.lambdaQuery(NewStudentGroup.class).last("limit 1"));
            if (testGroup != null) {
                log.info("单条查询结果 - ID: {}, 群名称: {}, joinInUser: {}",
                        testGroup.getId(), testGroup.getGroupName(), testGroup.getJoinInUser());

                // 检查 joinInUser 是否为空
                if (testGroup.getJoinInUser() == null) {
                    log.warn("joinInUser 字段为 null，可能存在类型转换问题");
                } else if (testGroup.getJoinInUser().isEmpty()) {
                    log.warn("joinInUser 字段为空列表");
                } else {
                    log.info("joinInUser 字段正常，包含 {} 个用户: {}",
                            testGroup.getJoinInUser().size(), testGroup.getJoinInUser());
                }
            } else {
                log.warn("没有找到测试数据");
            }

            // 测试分页查询
            log.info("开始测试分页查询...");
            NewStudentGroupQO qo = new NewStudentGroupQO();
            qo.setPage(1);
            qo.setSize(5);
            IPage<NewStudentGroupVO> pageResult = this.pageByParam(qo);

            log.info("分页查询结果 - 总记录数: {}, 当前页记录数: {}",
                    pageResult.getTotal(), pageResult.getRecords().size());

            for (NewStudentGroupVO vo : pageResult.getRecords()) {
                log.info("分页记录 - ID: {}, 群名称: {}, joinInUser: {}",
                        vo.getId(), vo.getGroupName(), vo.getJoinInUser());
            }

        } catch (Exception e) {
            log.error("测试 joinInUser 查询时发生异常", e);
        }
    }
}

