package com.lanshan.base.commonservice.typehandler;

import com.alibaba.nacos.common.utils.JacksonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description String 集合类型处理器，专门处理 PostgreSQL jsonb 类型
 */
@MappedJdbcTypes({JdbcType.OTHER, JdbcType.VARCHAR})
@MappedTypes(List.class)
public class StringListTypeHandler extends BaseTypeHandler<List<String>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType) throws SQLException {
        // 处理 PostgreSQL jsonb 类型
        PGobject jsonbObject = new PGobject();
        jsonbObject.setType("jsonb");
        jsonbObject.setValue(JacksonUtils.toJson(parameter));
        ps.setObject(i, jsonbObject);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getStringList(rs.getObject(columnName));
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getStringList(rs.getObject(columnIndex));
    }

    @Override
    public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getStringList(cs.getObject(columnIndex));
    }

    /**
     * 处理从数据库获取的对象，支持 PostgreSQL PGobject 和普通字符串
     */
    private List<String> getStringList(Object value) {
        if (value == null) {
            return Collections.emptyList();
        }

        String jsonString = null;

        // 处理 PostgreSQL PGobject
        if (value instanceof PGobject) {
            PGobject pgObject = (PGobject) value;
            if (Objects.nonNull(pgObject.getValue()) &&
                ("jsonb".equals(pgObject.getType()) || "json".equals(pgObject.getType()))) {
                jsonString = pgObject.getValue();
            }
        }
        // 处理普通字符串
        else if (value instanceof String) {
            jsonString = (String) value;
        }

        if (StringUtils.isNotBlank(jsonString)) {
            try {
                return JacksonUtils.toObj(jsonString, new TypeReference<List<String>>() {});
            } catch (Exception e) {
                // 如果解析失败，记录日志并返回空列表
                System.err.println("Failed to parse JSON string to List<String>: " + jsonString + ", error: " + e.getMessage());
                return Collections.emptyList();
            }
        }

        return Collections.emptyList();
    }
}
