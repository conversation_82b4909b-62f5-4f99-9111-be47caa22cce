package com.lanshan.base.commonservice.welcomenewstudent.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentGroup;
import com.lanshan.base.commonservice.welcomenewstudent.qo.NewStudentGroupQO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 新生群表(NewStudentGroup)表数据库访问层
 *
 * <AUTHOR>
 */
public interface NewStudentGroupDao extends BaseMapper<NewStudentGroup> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<NewStudentGroup> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<NewStudentGroup> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<NewStudentGroup> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<NewStudentGroup> entities);

    /**
     * 分页查询
     *
     * @param page 分页对象
     * @param qo   查询参数
     * @return
     */
    Page<NewStudentGroup> pageByParam(Page<NewStudentGroup> page, NewStudentGroupQO qo);
}

