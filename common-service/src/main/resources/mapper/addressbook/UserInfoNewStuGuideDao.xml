<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.addressbook.dao.UserInfoNewStuGuideDao">

    <resultMap type="com.lanshan.base.commonservice.addressbook.entity.UserInfoNewStuGuide" id="UserInfoNewStuGuideMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="BOOLEAN"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.user_info_new_stu_guide(title, content, status, creator, create_date, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.title}, #{entity.content}, #{entity.status}, #{entity.creator}, #{entity.createDate},
             #{entity.updateDate})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.user_info_new_stu_guide(title, content, status, creator, create_date, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.title}, #{entity.content}, #{entity.status}, #{entity.creator}, #{entity.createDate},
             #{entity.updateDate})
        </foreach>
        ON CONFLICT(id) DO update set title       = EXCLUDED.title,
                                      content     = EXCLUDED.content,
                                      status      = EXCLUDED.status,
                                      creator     = EXCLUDED.creator,
                                      create_date = EXCLUDED.create_date,
                                      update_date = EXCLUDED.update_date
    </insert>

    <select id="pageByParam" resultType="com.lanshan.base.commonservice.addressbook.vo.UserInfoNewStuGuideVO">
        SELECT user_info_new_stu_guide.*,
               public.sys_user.nick_name AS creator_name
        FROM addressbook.user_info_new_stu_guide
                 LEFT JOIN public.sys_user ON addressbook.user_info_new_stu_guide.creator = public.sys_user.user_name
        <where>
            <if test="qo.title != null and qo.title != ''">
                AND user_info_new_stu_guide.title LIKE CONCAT('%', #{qo.title}, '%')
            </if>
            <if test="qo.status != null">
                AND user_info_new_stu_guide.status = #{qo.status}
            </if>
            <if test="qo.creator != null and qo.creator != ''">
                AND user_info_new_stu_guide.creator = #{qo.creator}
            </if>
        </where>
        ORDER BY sort, user_info_new_stu_guide.update_date DESC
    </select>
</mapper>

